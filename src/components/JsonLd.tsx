"use client";

import { useEffect } from 'react';

export default function JsonLd() {
  useEffect(() => {
    // This script will only run on the client side
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify({
      "@context": "https://schema.org",
      "@type": "ProfessionalService",
      "name": "<PERSON><PERSON>ov - Web Developer",
      "description": "I craft high-conversion websites that help businesses grow their online presence and increase sales.",
      "image": "https://timodev.com/og-image.jpg",
      "url": "https://timodev.com",
      "telephone": "+1234567890", // Replace with your actual phone number
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Your City", // Replace with your actual location
        "addressRegion": "Your Region",
        "addressCountry": "Your Country"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "0", // Replace with your actual coordinates
        "longitude": "0"
      },
      "openingHoursSpecification": {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday"
        ],
        "opens": "09:00",
        "closes": "17:00"
      },
      "sameAs": [
        "https://twitter.com/yourusername", // Replace with your actual social media profiles
        "https://www.linkedin.com/in/yourusername/",
        "https://github.com/yourusername"
      ],
      "priceRange": "$$",
      "serviceArea": {
        "@type": "GeoCircle",
        "geoMidpoint": {
          "@type": "GeoCoordinates",
          "latitude": "0", // Replace with your actual coordinates
          "longitude": "0"
        },
        "geoRadius": "50000"
      },
      "makesOffer": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "High-Converting Website Development",
            "description": "Custom websites designed to convert visitors into customers."
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Business Automation",
            "description": "Automate your business processes to save time and increase efficiency."
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Google and Meta Ads",
            "description": "Targeted advertising campaigns to reach your ideal customers."
          }
        }
      ]
    });
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, []);

  return null;
}
