import { ImageResponse } from 'next/og';
 
export const runtime = 'edge';
 
export const alt = '<PERSON><PERSON> - High-Converting Business Websites';
export const size = {
  width: 1200,
  height: 630,
};
 
export const contentType = 'image/png';
 
export default async function Image() {
  return new ImageResponse(
    (
      <div
        style={{
          fontSize: 48,
          background: 'linear-gradient(to bottom, #ffffff, #f5f5f5)',
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 48,
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 24,
            background: 'white',
            padding: 48,
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            width: '90%',
            height: '80%',
          }}
        >
          <div
            style={{
              fontSize: 24,
              background: 'rgba(20, 184, 166, 0.1)',
              color: 'rgb(20, 184, 166)',
              padding: '8px 16px',
              borderRadius: 9999,
              marginBottom: 24,
            }}
          >
            Business Website Developer & Designer
          </div>
          <div
            style={{
              background: 'linear-gradient(to right, #2563eb, #14b8a6)',
              backgroundClip: 'text',
              color: 'transparent',
              fontSize: 64,
              fontWeight: 'bold',
              marginBottom: 24,
              textAlign: 'center',
            }}
          >
            High-Converting Business Websites
          </div>
          <div
            style={{
              fontSize: 32,
              color: 'rgba(0, 0, 0, 0.8)',
              maxWidth: 800,
              textAlign: 'center',
              marginBottom: 32,
            }}
          >
            Helping businesses grow their online presence and increase sales
          </div>
          <div
            style={{
              fontSize: 24,
              fontWeight: 'bold',
            }}
          >
            Timoor Nurzhanov
          </div>
        </div>
      </div>
    ),
    {
      ...size,
    }
  );
}
