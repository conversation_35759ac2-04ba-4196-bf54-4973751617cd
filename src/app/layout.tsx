import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Analytics } from "@vercel/analytics/react"

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'https://timodev.com'),
  title: "<PERSON><PERSON>ov | High-Converting Websites for Businesses",
  description: "I craft high-conversion websites that help businesses grow their online presence and increase sales. Expert web developer specializing in business websites.",
  keywords: ["<PERSON><PERSON>", "<PERSON><PERSON>ov web developer", "<PERSON><PERSON> website designer", "<PERSON><PERSON>", "<PERSON><PERSON>", "business websites", "high-converting websites", "web developer for business", "professional business website", "website design for businesses", "increase sales website", "business web development", "SEO optimized business websites", "custom business websites", "Galway web developer", "Ireland web designer"],
  authors: [{ name: "Timoor Nurzhanov", url: "https://timodev.com" }],
  creator: "Timoor Nurzhanov",
  publisher: "Timoor Nurzhanov",
  icons: {
    icon: [
      { url: "/favicon/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon/favicon-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/favicon/favicon.ico" }
    ],
    apple: [
      { url: "/favicon/apple-touch-icon.png" }
    ],
    other: [
      { rel: "android-chrome-192x192", url: "/favicon/android-chrome-192x192.png" },
      { rel: "android-chrome-512x512", url: "/favicon/android-chrome-512x512.png" },
      { rel: "manifest", url: "/favicon/site.webmanifest" }
    ],
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://timodev.com",
    title: "Timoor Nurzhanov | High-Converting Websites for Businesses",
    description: "I craft high-conversion websites that help businesses grow their online presence and increase sales. Expert web developer specializing in business websites.",
    siteName: "Timoor Nurzhanov",
    images: [
      {
        url: "https://opengraph.b-cdn.net/production/images/e71b36a4-84d8-43b4-9f3a-22f267803859.jpg?token=PwcczQJZtSEFR0yBGBJq_bp2UCEA-jTHXGfyjOvV8PA&height=630&width=1200&expires=***********",
        width: 1200,
        height: 630,
        alt: "Timoor Nurzhanov - Web Developer & Designer"
      }
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Timoor Nurzhanov | High-Converting Websites for Businesses",
    description: "I craft high-conversion websites that help businesses grow their online presence and increase sales. Expert web developer specializing in business websites.",
    images: ["https://opengraph.b-cdn.net/production/images/e71b36a4-84d8-43b4-9f3a-22f267803859.jpg?token=PwcczQJZtSEFR0yBGBJq_bp2UCEA-jTHXGfyjOvV8PA&height=630&width=1200&expires=***********"],
  },
  alternates: {
    canonical: "https://timodev.com",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
      'max-video-preview': -1,
    },
  },
};

// JSON-LD structured data for better SEO
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Person",
  "name": "Timoor Nurzhanov",
  "url": "https://timodev.com",
  "image": "https://timodev.com/timotorosquare.jpg",
  "sameAs": [
    // Add your social media profiles here if you have any
    // "https://linkedin.com/in/timoornurzhanov",
    // "https://github.com/timoornurzhanov"
  ],
  "jobTitle": "Web Developer & Designer",
  "worksFor": {
    "@type": "Organization",
    "name": "Timoor Nurzhanov Web Development"
  },
  "description": "I craft high-conversion websites that help businesses grow their online presence and increase sales. Expert web developer specializing in business websites.",
  "knowsAbout": ["Web Development", "Web Design", "Business Websites", "Conversion Optimization", "SEO", "Digital Marketing"],
  "makesOffer": [
    {
      "@type": "Offer",
      "name": "High-Converting Websites",
      "description": "Custom business websites designed with conversion psychology principles that turn visitors into customers."
    },
    {
      "@type": "Offer",
      "name": "Digital Marketing",
      "description": "Strategic ad campaigns for businesses that target your ideal customers and maximize your return on ad spend."
    },
    {
      "@type": "Offer",
      "name": "Website Automation",
      "description": "Streamline business operations with custom website workflows, integrations, and automated processes."
    }
  ]
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
        <Analytics />
      </body>
    </html>
  );
}
